import {
	Button,
	Flex,
	FormControl,
	FormLabel,
	Heading,
	Input,
	Stack,
	Avatar,
	Box,
	Text,
	IconButton,
	Divider,
	InputGroup,
	InputLeftElement,
	Textarea,
	useColorModeValue,
} from "@chakra-ui/react";
import { useRef, useState, useEffect } from "react";
import { useRecoilState } from "recoil";
import { useNavigate } from "react-router-dom";
import { ArrowBackIcon } from "@chakra-ui/icons";
import { FaUser, FaEnvelope, FaLock, FaIdCard, FaQuoteLeft } from "react-icons/fa";
import { userAtom } from "../atoms";
import usePreviewImg from "../hooks/usePreviewImg";
import useShowToast from "../hooks/useShowToast";
import useUserEvents from "../hooks/useUserEvents";
import { fetchWithSession } from "../utils/api";

export default function UpdateProfilePage() {
	const navigate = useNavigate();
	const [user, setUser] = useRecoilState(userAtom);
	const { emitUserUpdate } = useUserEvents();
	const [inputs, setInputs] = useState({
		name: "",
		username: "",
		email: "",
		bio: "",
		password: "",
	});

	// Theme-aware colors
	const bgColor = useColorModeValue("white", "#101010");
	const borderColor = useColorModeValue("gray.200", "gray.700");
	const textColor = useColorModeValue("gray.800", "white");
	const labelColor = useColorModeValue("gray.600", "gray.300");
	const inputBg = useColorModeValue("gray.50", "rgba(0, 0, 0, 0.2)");
	const inputBorderColor = useColorModeValue("gray.300", "gray.600");
	const inputHoverBorderColor = useColorModeValue("gray.400", "gray.500");
	const placeholderColor = useColorModeValue("gray.400", "gray.500");
	const dividerColor = useColorModeValue("rgba(0, 204, 133, 0.3)", "rgba(0, 204, 133, 0.2)");
	const cancelButtonBorder = useColorModeValue("gray.300", "gray.600");
	const cancelButtonHoverBg = useColorModeValue("gray.100", "rgba(255, 255, 255, 0.1)");
	const cancelButtonHoverBorder = useColorModeValue("gray.400", "gray.500");
	const buttonTextColor = useColorModeValue("gray.700", "white");
	const backButtonColor = useColorModeValue("gray.600", "white");
	const helperTextColor = useColorModeValue("gray.500", "gray.500");

	useEffect(() => {
		if (user) {
			setInputs({
				name: user.name || "",
				username: user.username || "",
				email: user.email || "",
				bio: user.bio || "",
				password: "",
			});
		}
	}, [user]);
	const fileRef = useRef(null);
	const [updating, setUpdating] = useState(false);

	const showToast = useShowToast();

	const { handleImageChange, imgUrl } = usePreviewImg();

	const handleSubmit = async (e) => {
		e.preventDefault();
		if (updating) return;
		if (inputs.password && inputs.password.length < 6) {
			showToast("Error", "Password must be at least 6 characters", "error");
			return;
		}
		setUpdating(true);
		try {
			const res = await fetchWithSession(`/api/users/update/${user._id}`, {
				method: "PUT",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ ...inputs, profilePic: imgUrl }),
			});
			if (res.ok) {
				const data = await res.json(); // updated user object
				showToast("Success", "Profile updated successfully", "success");
				setUser(data);
				localStorage.setItem("user-threads", JSON.stringify(data));

				// Emit global user update event to synchronize all components
				emitUserUpdate(data);

				// Navigate to the profile page after successful update
				setTimeout(() => {
					navigate(`/${data.username}`);
				}, 500); // Small delay to show the success toast
			} else {
				const errorData = await res.json().catch(() => ({ error: 'Failed to update profile' }));
				showToast("Error", errorData.error || 'Failed to update profile', "error");
			}
		} catch (error) {
			showToast("Error", error, "error");
		} finally {
			setUpdating(false);
		}
	};

	return (
		<form onSubmit={handleSubmit}>
			<Flex direction="column" align="center" justify="center" my={{ base: 2, md: 4 }} px={4} pb={{ base: "180px", md: "20px" }}>
				{/* Back Button */}
				<Box alignSelf="flex-start" mb={{ base: 2, md: 3 }}>
					<IconButton
						icon={<ArrowBackIcon boxSize={5} />}
						aria-label="Go back"
						variant="ghost"
						color={backButtonColor}
						_hover={{
							color: "rgba(0, 204, 133, 0.9)",
							bg: "rgba(0, 204, 133, 0.1)"
						}}
						onClick={() => navigate(`/${user.username}`)}
						size="md"
						borderRadius="md"
					/>
				</Box>

				{/* Main Container */}
				<Stack
					spacing={{ base: 3, md: 5 }}
					w="full"
					maxW="md"
					bg={bgColor}
					rounded="xl"
					borderWidth="1px"
					borderColor={borderColor}
					boxShadow="0 4px 20px rgba(0, 0, 0, 0.3)"
					p={{ base: 4, md: 6 }}
					className="glass-card"
					position="relative"
				>
					{/* Header */}
					<Flex direction="column" align="center" mb={{ base: 1, md: 2 }}>
						<Heading
							lineHeight={1.1}
							fontSize={{ base: "xl", sm: "2xl", md: "3xl" }}
							bgGradient="linear(to-r, rgba(0, 204, 133, 0.8), rgba(0, 121, 185, 0.8))"
							bgClip="text"
							fontWeight="bold"
						>
							Edit Your Profile
						</Heading>
						<Text fontSize="sm" color={labelColor} mt={{ base: 0.5, md: 1 }}>
							Update your personal information
						</Text>
					</Flex>

					<Divider borderColor={dividerColor} />

					{/* Avatar Section */}
					<FormControl id="userName">
						<Flex direction="column" align="center" justify="center">
							<Box
								position="relative"
								mb={{ base: 2, md: 3 }}
								cursor="pointer"
								onClick={() => fileRef.current.click()}
								transition="all 0.3s ease"
								_hover={{ transform: "scale(1.05)" }}
							>
								<Avatar
									size={{ base: "xl", md: "2xl" }}
									src={imgUrl || user.profilePic}
									boxShadow="0 4px 12px rgba(0, 0, 0, 0.2)"
									border="3px solid"
									borderColor="rgba(0, 204, 133, 0.3)"
								/>
								<Box
									position="absolute"
									bottom="0"
									right="0"
									bg="rgba(0, 204, 133, 0.8)"
									p={1}
									borderRadius="full"
									boxShadow="0 2px 6px rgba(0, 0, 0, 0.2)"
								>
									<FaUser color="white" size={14} />
								</Box>
							</Box>
							<Button
								size="sm"
								bg="rgba(0, 204, 133, 0.2)"
								color={buttonTextColor}
								borderWidth="1px"
								borderColor="rgba(0, 204, 133, 0.5)"
								_hover={{
									bg: "rgba(0, 204, 133, 0.3)",
									transform: "translateY(-2px)",
									borderColor: "rgba(0, 204, 133, 0.7)"
								}}
								transition="all 0.2s"
								borderRadius="md"
								fontWeight="medium"
								onClick={() => fileRef.current.click()}
								boxShadow="0 2px 6px rgba(0, 0, 0, 0.1)"
								px={4}
								py={2}
								_active={{
									transform: "scale(0.98)",
									boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)"
								}}
								leftIcon={<FaUser size={14} />}
							>
								Change Avatar
							</Button>
							<Input type="file" hidden ref={fileRef} onChange={handleImageChange} />
						</Flex>
					</FormControl>

					<Divider borderColor={dividerColor} />

					{/* Form Fields */}
					<Stack spacing={{ base: 2, md: 3 }}>
						{/* Full Name */}
						<FormControl>
							<FormLabel fontWeight="medium" color={labelColor}>Full Name</FormLabel>
							<InputGroup>
								<InputLeftElement pointerEvents="none">
									<FaUser color="rgba(0, 204, 133, 0.6)" />
								</InputLeftElement>
								<Input
									placeholder="John Doe"
									value={inputs.name}
									onChange={(e) => setInputs({ ...inputs, name: e.target.value })}
									_placeholder={{ color: placeholderColor }}
									type="text"
									bg={inputBg}
									borderColor={inputBorderColor}
									borderRadius="md"
									color={textColor}
									_hover={{ borderColor: inputHoverBorderColor }}
									_focus={{
										borderColor: "rgba(0, 204, 133, 0.6)",
										boxShadow: "0 0 0 1px rgba(0, 204, 133, 0.6)"
									}}
									transition="all 0.3s ease"
								/>
							</InputGroup>
						</FormControl>

						{/* Username */}
						<FormControl>
							<FormLabel fontWeight="medium" color={labelColor}>Username</FormLabel>
							<InputGroup>
								<InputLeftElement pointerEvents="none">
									<FaIdCard color="rgba(0, 204, 133, 0.6)" />
								</InputLeftElement>
								<Input
									placeholder="johndoe"
									value={inputs.username}
									onChange={(e) => setInputs({ ...inputs, username: e.target.value })}
									_placeholder={{ color: placeholderColor }}
									type="text"
									bg={inputBg}
									borderColor={inputBorderColor}
									borderRadius="md"
									color={textColor}
									_hover={{ borderColor: inputHoverBorderColor }}
									_focus={{
										borderColor: "rgba(0, 204, 133, 0.6)",
										boxShadow: "0 0 0 1px rgba(0, 204, 133, 0.6)"
									}}
									transition="all 0.3s ease"
								/>
							</InputGroup>
						</FormControl>

						{/* Email */}
						<FormControl>
							<FormLabel fontWeight="medium" color={labelColor}>Email Address</FormLabel>
							<InputGroup>
								<InputLeftElement pointerEvents="none">
									<FaEnvelope color="rgba(0, 204, 133, 0.6)" />
								</InputLeftElement>
								<Input
									placeholder="<EMAIL>"
									value={inputs.email}
									onChange={(e) => setInputs({ ...inputs, email: e.target.value })}
									_placeholder={{ color: placeholderColor }}
									type="email"
									bg={inputBg}
									borderColor={inputBorderColor}
									borderRadius="md"
									color={textColor}
									_hover={{ borderColor: inputHoverBorderColor }}
									_focus={{
										borderColor: "rgba(0, 204, 133, 0.6)",
										boxShadow: "0 0 0 1px rgba(0, 204, 133, 0.6)"
									}}
									transition="all 0.3s ease"
								/>
							</InputGroup>
						</FormControl>

						{/* Bio */}
						<FormControl>
							<FormLabel fontWeight="medium" color={labelColor}>Bio</FormLabel>
							<InputGroup>
								<InputLeftElement pointerEvents="none">
									<FaQuoteLeft color="rgba(0, 204, 133, 0.6)" />
								</InputLeftElement>
								<Textarea
									placeholder="Tell us about yourself"
									value={inputs.bio}
									onChange={(e) => setInputs({ ...inputs, bio: e.target.value })}
									_placeholder={{ color: placeholderColor }}
									bg={inputBg}
									borderColor={inputBorderColor}
									borderRadius="md"
									color={textColor}
									_hover={{ borderColor: inputHoverBorderColor }}
									_focus={{
										borderColor: "rgba(0, 204, 133, 0.6)",
										boxShadow: "0 0 0 1px rgba(0, 204, 133, 0.6)"
									}}
									transition="all 0.3s ease"
									pl={10}
									minH={{ base: "60px", md: "80px" }}
									resize="vertical"
								/>
							</InputGroup>
						</FormControl>

						{/* Password */}
						<FormControl>
							<FormLabel fontWeight="medium" color={labelColor}>Password</FormLabel>
							<InputGroup>
								<InputLeftElement pointerEvents="none">
									<FaLock color="rgba(0, 204, 133, 0.6)" />
								</InputLeftElement>
								<Input
									placeholder="Leave blank to keep current password"
									value={inputs.password}
									onChange={(e) => setInputs({ ...inputs, password: e.target.value })}
									_placeholder={{ color: placeholderColor }}
									type="password"
									bg={inputBg}
									borderColor={inputBorderColor}
									borderRadius="md"
									color={textColor}
									_hover={{ borderColor: inputHoverBorderColor }}
									_focus={{
										borderColor: "rgba(0, 204, 133, 0.6)",
										boxShadow: "0 0 0 1px rgba(0, 204, 133, 0.6)"
									}}
									transition="all 0.3s ease"
								/>
							</InputGroup>
							<Text fontSize="xs" color={helperTextColor} mt={1}>
								Must be at least 6 characters
							</Text>
						</FormControl>
					</Stack>

					<Divider borderColor={dividerColor} mt={{ base: 1, md: 2 }} />

					{/* Action Buttons */}
					<Stack spacing={{ base: 2, md: 3 }} direction={["column", "row"]} pt={{ base: 1, md: 2 }}>
						<Button
							bg="transparent"
							color={buttonTextColor}
							borderWidth="1px"
							borderColor={cancelButtonBorder}
							_hover={{
								bg: cancelButtonHoverBg,
								transform: "translateY(-2px)",
								borderColor: cancelButtonHoverBorder
							}}
							transition="all 0.2s"
							borderRadius="md"
							fontWeight="medium"
							w="full"
							onClick={() => navigate(`/${user.username}`)}
							boxShadow="0 2px 6px rgba(0, 0, 0, 0.1)"
							_active={{
								transform: "scale(0.98)",
								boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)"
							}}
						>
							Cancel
						</Button>
						<Button
							bg="rgba(0, 204, 133, 0.2)"
							color={buttonTextColor}
							borderWidth="1px"
							borderColor="rgba(0, 204, 133, 0.5)"
							_hover={{
								bg: "rgba(0, 204, 133, 0.3)",
								transform: "translateY(-2px)",
								borderColor: "rgba(0, 204, 133, 0.7)"
							}}
							transition="all 0.2s"
							borderRadius="md"
							fontWeight="medium"
							w="full"
							type="submit"
							isLoading={updating}
							boxShadow="0 2px 6px rgba(0, 0, 0, 0.1)"
							_active={{
								transform: "scale(0.98)",
								boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)"
							}}
						>
							Save Changes
						</Button>
					</Stack>
				</Stack>
			</Flex>
		</form>
	);
}
