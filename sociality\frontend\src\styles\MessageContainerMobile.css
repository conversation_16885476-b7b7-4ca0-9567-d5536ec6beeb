/**
 * Mobile Message Container Enhancements
 * Ensures proper message visibility above message input and navigation
 */

/* ===== MOBILE MESSAGE CONTAINER FIXES ===== */

@media (max-width: 767px) {
  /* Ensure message container doesn't overlap with fixed elements */
  .message-container {
    /* Add extra bottom margin to prevent overlap with message input */
    margin-bottom: 160px !important;
    
    /* Ensure proper height calculation */
    height: calc(100vh - 320px) !important;
    max-height: calc(100vh - 320px) !important;
    
    /* Prevent content from being hidden behind fixed elements */
    padding-bottom: 20px !important;
  }
  
  /* React Window List mobile adjustments */
  .react-window-list {
    /* Add bottom padding to ensure last message is visible */
    padding-bottom: 20px !important;
  }
  
  /* Message items mobile spacing */
  .message-item {
    margin-bottom: 8px !important;
  }
  
  /* Last message extra spacing */
  .message-item:last-child {
    margin-bottom: 40px !important;
  }
}

/* ===== MOBILE LANDSCAPE ADJUSTMENTS ===== */

@media (max-width: 767px) and (orientation: landscape) {
  .message-container {
    height: calc(100vh - 280px) !important;
    max-height: calc(100vh - 280px) !important;
    margin-bottom: 120px !important;
  }
}

/* ===== SMALL MOBILE SCREENS ===== */

@media (max-width: 374px) {
  .message-container {
    height: calc(100vh - 300px) !important;
    max-height: calc(100vh - 300px) !important;
    margin-bottom: 140px !important;
  }
}

/* ===== LARGE MOBILE SCREENS ===== */

@media (min-width: 414px) and (max-width: 767px) {
  .message-container {
    height: calc(100vh - 340px) !important;
    max-height: calc(100vh - 340px) !important;
    margin-bottom: 180px !important;
  }
}

/* ===== DESKTOP STYLES ===== */

@media (min-width: 768px) {
  .message-container {
    /* Reset mobile styles for desktop */
    margin-bottom: 0 !important;
    height: calc(100vh - 200px) !important;
    max-height: calc(100vh - 200px) !important;
    padding-bottom: 100px !important;
  }
  
  .message-item:last-child {
    margin-bottom: 20px !important;
  }
}

/* ===== SAFE AREA SUPPORT ===== */

@supports (padding: max(0px)) {
  @media (max-width: 767px) {
    .message-container {
      /* Account for safe areas on devices with notches */
      padding-bottom: max(20px, calc(20px + env(safe-area-inset-bottom))) !important;
      margin-bottom: max(160px, calc(160px + env(safe-area-inset-bottom))) !important;
    }
  }
}

/* ===== MESSAGE INPUT POSITIONING FIXES ===== */

@media (max-width: 767px) {
  /* Ensure message input stays above navigation */
  .message-input-container {
    position: fixed !important;
    bottom: 80px !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 200 !important;
  }
  
  /* Add backdrop blur for better visibility */
  .message-input-container::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 0;
    right: 0;
    bottom: 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: -1;
  }
}

/* ===== SCROLL BEHAVIOR IMPROVEMENTS ===== */

@media (max-width: 767px) {
  /* Smooth scrolling for message container */
  .message-container {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Hide scrollbars but keep functionality */
  .message-container::-webkit-scrollbar {
    display: none;
  }
  
  .react-window-list::-webkit-scrollbar {
    display: none;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

@media (max-width: 767px) {
  /* GPU acceleration for smooth scrolling */
  .message-container,
  .react-window-list {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  
  /* Optimize message rendering */
  .message-item {
    contain: layout style paint;
    will-change: auto;
  }
}
