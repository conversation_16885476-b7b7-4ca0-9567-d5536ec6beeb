/**
 * Test script to verify responsive breakpoints
 * Run this in browser console to test different screen sizes
 */

// Import functions (you'll need to copy these into console)
const DEVICE_BREAKPOINTS = {
  xs: '280px',
  sm: '320px',
  md: '360px',
  lg: '375px',
  xl: '390px',
  '2xl': '414px',
  '3xl': '428px',
  '3xl-plus': '430px',
  '3xl-max': '480px',
  '3xl-ultra': '540px',
  '3xl-hybrid': '600px',
  '3xl-extended': '680px',
  '4xl-mobile': '760px',
  '4xl': '768px',
  '5xl': '1024px'
};

const getCurrentDeviceCategory = () => {
  const width = window.innerWidth;

  if (width < 280) return 'xs';
  if (width < 320) return 'sm';
  if (width < 360) return 'md';
  if (width < 375) return 'lg';
  if (width < 390) return 'xl';
  if (width < 414) return '2xl';
  if (width < 428) return '3xl';
  if (width < 430) return '3xl-plus';
  if (width < 480) return '3xl-max';
  if (width < 540) return '3xl-ultra';
  if (width < 600) return '3xl-hybrid';
  if (width < 680) return '3xl-extended';
  if (width < 760) return '4xl-mobile';
  if (width < 768) return '4xl';
  return '5xl';
};

const shouldUseBottomNavigation = () => {
  return window.innerWidth < parseInt(DEVICE_BREAKPOINTS['4xl']);
};

// Test function
function testResponsiveBreakpoints() {
  const width = window.innerWidth;
  const height = window.innerHeight;
  const category = getCurrentDeviceCategory();
  const shouldUseBottom = shouldUseBottomNavigation();
  
  console.log('=== Responsive Navigation Test ===');
  console.log(`Screen Size: ${width} x ${height}`);
  console.log(`Device Category: ${category}`);
  console.log(`Should Use Bottom Navigation: ${shouldUseBottom}`);
  console.log(`Is Your Specific Case (760-430 x 932): ${width >= 760 && width <= 767 && height >= 930}`);
  
  // Test specific breakpoints
  const testCases = [
    { width: 430, height: 932, description: 'iPhone 14 Pro Max (430x932)' },
    { width: 760, height: 932, description: 'Your specific case (760x932)' },
    { width: 428, height: 926, description: 'iPhone 14 Pro Max (428x926)' },
    { width: 414, height: 896, description: 'iPhone 11 Pro Max' },
    { width: 375, height: 812, description: 'iPhone X/11 Pro' },
    { width: 768, height: 1024, description: 'iPad Mini' },
    { width: 1024, height: 768, description: 'Desktop' }
  ];
  
  console.log('\n=== Test Cases ===');
  testCases.forEach(testCase => {
    // Simulate the width for testing
    const originalWidth = window.innerWidth;
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: testCase.width
    });
    
    const category = getCurrentDeviceCategory();
    const shouldUseBottom = shouldUseBottomNavigation();
    
    console.log(`${testCase.description}:`);
    console.log(`  Size: ${testCase.width}x${testCase.height}`);
    console.log(`  Category: ${category}`);
    console.log(`  Bottom Nav: ${shouldUseBottom}`);
    console.log(`  Expected: ${testCase.width < 768 ? 'Bottom' : 'Left Side'}`);
    console.log('');
    
    // Restore original width
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: originalWidth
    });
  });
}

// Run the test
testResponsiveBreakpoints();

// Add resize listener for live testing
window.addEventListener('resize', () => {
  setTimeout(() => {
    console.clear();
    testResponsiveBreakpoints();
  }, 100);
});

console.log('\n=== Instructions ===');
console.log('1. Resize your browser window to test different breakpoints');
console.log('2. Check the console output for real-time updates');
console.log('3. Pay special attention to the 760px width case');
console.log('4. Verify that devices < 768px use bottom navigation');
console.log('5. Verify that devices >= 768px use left-side navigation');
