/**
 * Advanced Responsive Breakpoints for Mobile Devices
 * Provides granular control over different smartphone sizes
 */

// Device categories with specific breakpoints
export const DEVICE_BREAKPOINTS = {
  // Ultra-small devices (older smartphones, small screens)
  xs: '280px',    // iPhone 4, very old Android

  // Small smartphones
  sm: '320px',    // iPhone SE (1st gen), Galaxy S3

  // Standard small smartphones
  md: '360px',    // Galaxy S5, Pixel 2

  // Standard smartphones
  lg: '375px',    // iPhone 6/7/8, iPhone SE (2nd gen)

  // Large smartphones
  xl: '390px',    // iPhone 12 mini, iPhone 13 mini

  // Extra large smartphones
  '2xl': '414px', // iPhone 6/7/8 Plus, iPhone 11

  // Pro smartphones
  '3xl': '428px', // iPhone 12/13/14 Pro Max (428x926)

  // Latest Pro smartphones
  '3xl-plus': '430px', // iPhone 14 Pro Max (430x932)

  // Large mobile devices / Small tablets in portrait
  '3xl-max': '480px', // Large phones, foldables unfolded

  // Extra large mobile devices
  '3xl-ultra': '540px', // Very large phones, small tablets

  // Mobile-tablet hybrid range
  '3xl-hybrid': '600px', // Large mobile devices, small tablets

  // Large mobile/small tablet range
  '3xl-extended': '680px', // Covers your 760-430 case when rotated

  // Small tablets (portrait) / Large mobile (landscape)
  '4xl-mobile': '760px', // Specifically covers 760px width devices

  // Tablets (small)
  '4xl': '768px', // iPad mini, small tablets

  // Desktop
  '5xl': '1024px' // Desktop and larger
};

// Chakra UI compatible breakpoint object
export const CHAKRA_BREAKPOINTS = {
  base: '0px',
  xs: DEVICE_BREAKPOINTS.xs,
  sm: DEVICE_BREAKPOINTS.sm,
  md: DEVICE_BREAKPOINTS.md,
  lg: DEVICE_BREAKPOINTS.lg,
  xl: DEVICE_BREAKPOINTS.xl,
  '2xl': DEVICE_BREAKPOINTS['2xl'],
  '3xl': DEVICE_BREAKPOINTS['3xl'],
  '3xl-plus': DEVICE_BREAKPOINTS['3xl-plus'],
  '3xl-max': DEVICE_BREAKPOINTS['3xl-max'],
  '3xl-ultra': DEVICE_BREAKPOINTS['3xl-ultra'],
  '3xl-hybrid': DEVICE_BREAKPOINTS['3xl-hybrid'],
  '3xl-extended': DEVICE_BREAKPOINTS['3xl-extended'],
  '4xl-mobile': DEVICE_BREAKPOINTS['4xl-mobile'],
  '4xl': DEVICE_BREAKPOINTS['4xl'],
  '5xl': DEVICE_BREAKPOINTS['5xl']
};

/**
 * Get current device category based on screen width
 */
export const getCurrentDeviceCategory = () => {
  const width = window.innerWidth;

  if (width < 280) return 'xs';
  if (width < 320) return 'sm';
  if (width < 360) return 'md';
  if (width < 375) return 'lg';
  if (width < 390) return 'xl';
  if (width < 414) return '2xl';
  if (width < 428) return '3xl';
  if (width < 430) return '3xl-plus';
  if (width < 480) return '3xl-max';
  if (width < 540) return '3xl-ultra';
  if (width < 600) return '3xl-hybrid';
  if (width < 680) return '3xl-extended';
  if (width < 760) return '4xl-mobile';
  if (width < 768) return '4xl';
  return '5xl';
};

/**
 * Check if current device is in mobile range
 * Mobile includes all devices up to small tablets (768px)
 */
export const isMobileDevice = () => {
  return window.innerWidth < parseInt(DEVICE_BREAKPOINTS['4xl']);
};

/**
 * Check if device should use bottom navigation (mobile-style)
 * This includes all devices up to 768px width
 */
export const shouldUseBottomNavigation = () => {
  return window.innerWidth < parseInt(DEVICE_BREAKPOINTS['4xl']);
};

/**
 * Get responsive values based on device category
 */
export const getResponsiveValue = (values) => {
  const category = getCurrentDeviceCategory();
  
  // Return the most specific value available
  if (values[category]) return values[category];
  
  // Fallback logic
  const fallbackOrder = ['3xl', '2xl', 'xl', 'lg', 'md', 'sm', 'xs', 'base'];
  
  for (const fallback of fallbackOrder) {
    if (values[fallback]) return values[fallback];
  }
  
  return values.base || values[Object.keys(values)[0]];
};

/**
 * Dynamic spacing based on device size
 */
export const getDynamicSpacing = () => {
  const category = getCurrentDeviceCategory();

  const spacingMap = {
    xs: { gap: 4, padding: 6, margin: 4 },
    sm: { gap: 6, padding: 8, margin: 6 },
    md: { gap: 8, padding: 10, margin: 8 },
    lg: { gap: 10, padding: 12, margin: 10 },
    xl: { gap: 12, padding: 12, margin: 12 },
    '2xl': { gap: 12, padding: 12, margin: 12 },
    '3xl': { gap: 12, padding: 12, margin: 12 }, // iPhone 14 Pro Max (428x926)
    '3xl-plus': { gap: 14, padding: 12, margin: 12 }, // iPhone 14 Pro Max (430x932)
    '3xl-max': { gap: 14, padding: 14, margin: 12 }, // Large phones
    '3xl-ultra': { gap: 16, padding: 14, margin: 14 }, // Very large phones
    '3xl-hybrid': { gap: 16, padding: 16, margin: 14 }, // Mobile-tablet hybrid
    '3xl-extended': { gap: 18, padding: 16, margin: 16 }, // Large mobile devices
    '4xl-mobile': { gap: 18, padding: 18, margin: 16 }, // 760px width devices
    '4xl': { gap: 16, padding: 20, margin: 16 },
    '5xl': { gap: 20, padding: 24, margin: 20 }
  };

  return spacingMap[category] || spacingMap.lg;
};

/**
 * Dynamic button sizes based on device
 */
export const getDynamicButtonSize = () => {
  const category = getCurrentDeviceCategory();

  const buttonSizeMap = {
    xs: { minH: '32px', minW: '32px', iconSize: 16 },
    sm: { minH: '36px', minW: '36px', iconSize: 18 },
    md: { minH: '40px', minW: '40px', iconSize: 20 },
    lg: { minH: '42px', minW: '42px', iconSize: 22 },
    xl: { minH: '44px', minW: '44px', iconSize: 22 },
    '2xl': { minH: '44px', minW: '44px', iconSize: 22 },
    '3xl': { minH: '44px', minW: '44px', iconSize: 22 }, // iPhone 14 Pro Max (428x926)
    '3xl-plus': { minH: '46px', minW: '46px', iconSize: 24 }, // iPhone 14 Pro Max (430x932)
    '3xl-max': { minH: '48px', minW: '48px', iconSize: 24 }, // Large phones
    '3xl-ultra': { minH: '50px', minW: '50px', iconSize: 26 }, // Very large phones
    '3xl-hybrid': { minH: '52px', minW: '52px', iconSize: 26 }, // Mobile-tablet hybrid
    '3xl-extended': { minH: '54px', minW: '54px', iconSize: 28 }, // Large mobile devices
    '4xl-mobile': { minH: '56px', minW: '56px', iconSize: 28 }, // 760px width devices
    '4xl': { minH: '48px', minW: '48px', iconSize: 24 },
    '5xl': { minH: '44px', minW: '44px', iconSize: 22 }
  };

  return buttonSizeMap[category] || buttonSizeMap.lg;
};

/**
 * Dynamic logo size based on device
 */
export const getDynamicLogoSize = () => {
  const category = getCurrentDeviceCategory();
  
  const logoSizeMap = {
    xs: { size: 24, containerSize: 32 },
    sm: { size: 28, containerSize: 36 },
    md: { size: 30, containerSize: 38 },
    lg: { size: 32, containerSize: 40 },
    xl: { size: 34, containerSize: 42 },
    '2xl': { size: 36, containerSize: 44 },
    '3xl': { size: 38, containerSize: 46 },
    '4xl': { size: 40, containerSize: 48 },
    '5xl': { size: 32, containerSize: 40 }
  };
  
  return logoSizeMap[category] || logoSizeMap.lg;
};

/**
 * Get safe area insets for different devices
 */
export const getSafeAreaInsets = () => {
  const category = getCurrentDeviceCategory();
  
  // Different devices have different safe area requirements
  const safeAreaMap = {
    xs: { top: 8, bottom: 8, left: 8, right: 8 },
    sm: { top: 10, bottom: 10, left: 10, right: 10 },
    md: { top: 12, bottom: 12, left: 12, right: 12 },
    lg: { top: 14, bottom: 14, left: 14, right: 14 },
    xl: { top: 16, bottom: 16, left: 16, right: 16 },
    '2xl': { top: 18, bottom: 18, left: 18, right: 18 },
    '3xl': { top: 20, bottom: 20, left: 20, right: 20 },
    '4xl': { top: 24, bottom: 24, left: 24, right: 24 },
    '5xl': { top: 32, bottom: 32, left: 32, right: 32 }
  };
  
  return safeAreaMap[category] || safeAreaMap.lg;
};

/**
 * Check if device has notch/dynamic island
 */
export const hasNotch = () => {
  // Check for devices with notches/dynamic islands
  const hasNotchSupport = 'CSS' in window && CSS.supports('padding-top: env(safe-area-inset-top)');
  const userAgent = navigator.userAgent;
  
  // iPhone X and newer have notches/dynamic islands
  const isIPhoneWithNotch = /iPhone/.test(userAgent) && window.screen.height >= 812;
  
  return hasNotchSupport || isIPhoneWithNotch;
};

/**
 * Get orientation-specific adjustments
 */
export const getOrientationAdjustments = () => {
  const isLandscape = window.innerWidth > window.innerHeight;
  const category = getCurrentDeviceCategory();
  
  if (isLandscape && isMobileDevice()) {
    return {
      logoTop: getSafeAreaInsets().top / 2,
      navBottom: getSafeAreaInsets().bottom / 2,
      spacing: getDynamicSpacing().gap * 0.8,
      buttonSize: {
        ...getDynamicButtonSize(),
        minH: `${parseInt(getDynamicButtonSize().minH) * 0.9}px`
      }
    };
  }
  
  return {
    logoTop: getSafeAreaInsets().top,
    navBottom: getSafeAreaInsets().bottom,
    spacing: getDynamicSpacing().gap,
    buttonSize: getDynamicButtonSize()
  };
};

// Make functions available globally for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.responsiveBreakpoints = {
    getCurrentDeviceCategory,
    isMobileDevice,
    getResponsiveValue,
    getDynamicSpacing,
    getDynamicButtonSize,
    getDynamicLogoSize,
    getSafeAreaInsets,
    hasNotch,
    getOrientationAdjustments
  };
}
