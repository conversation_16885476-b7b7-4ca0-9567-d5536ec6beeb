/**
 * Dynamic Mobile Header Styles
 * Responsive header that adapts to all smartphone sizes
 */

/* ===== BASE MOBILE HEADER STYLES ===== */

.dynamic-mobile-header {
  position: relative;
  width: 100%;
  z-index: 1000;
}

/* ===== ULTRA-SMALL DEVICES (< 280px) ===== */
@media (max-width: 279px) {
  .mobile-logo-scroll {
    top: 6px !important;
    padding: 4px !important;
  }

  .mobile-logo-scroll img {
    width: 20px !important;
    height: 20px !important;
  }

  .glass-navbar {
    gap: 2px !important; /* Very small gap to fit all buttons */
    padding: 4px 2px !important; /* Minimal padding */
    padding-bottom: max(4px, calc(4px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove border radius for bottom-stuck appearance */
    bottom: 0 !important; /* Stick to bottom edge */
    left: 0 !important; /* Full width */
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important; /* Distribute buttons evenly */
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important; /* Add top border */
    display: flex !important;
    flex-wrap: nowrap !important;
    overflow-x: hidden !important;
  }

  .glass-navbar .chakra-button {
    min-height: 30px !important; /* Very small buttons */
    min-width: 30px !important;
    max-width: 45px !important; /* Small max width */
    padding: 3px !important; /* Minimal padding */
    flex: 1 !important; /* Equal width buttons */
    flex-shrink: 0 !important; /* Prevent shrinking */
  }

  .glass-navbar svg {
    width: 14px !important; /* Very small icons */
    height: 14px !important;
  }
}

/* ===== VERY SMALL DEVICES (280px - 319px) ===== */
@media (min-width: 280px) and (max-width: 319px) {
  .mobile-logo-scroll {
    top: 8px !important;
    padding: 6px !important;
  }

  .mobile-logo-scroll img {
    width: 24px !important;
    height: 24px !important;
  }

  .glass-navbar {
    gap: 3px !important; /* Small gap to fit all buttons */
    padding: 6px 3px !important; /* Reduced padding */
    padding-bottom: max(6px, calc(6px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove border radius for bottom-stuck appearance */
    bottom: 0 !important; /* Stick to bottom edge */
    left: 0 !important; /* Full width */
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important; /* Distribute buttons evenly */
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important; /* Add top border */
    display: flex !important;
    flex-wrap: nowrap !important;
    overflow-x: hidden !important;
  }

  .glass-navbar .chakra-button {
    min-height: 34px !important; /* Small buttons */
    min-width: 34px !important;
    max-width: 48px !important; /* Small max width */
    padding: 5px !important; /* Reduced padding */
    flex: 1 !important; /* Equal width buttons */
    flex-shrink: 0 !important; /* Prevent shrinking */
  }

  .glass-navbar svg {
    width: 16px !important; /* Small icons */
    height: 16px !important;
  }
}

/* ===== SMALL DEVICES (320px - 359px) - iPhone SE ===== */
@media (min-width: 320px) and (max-width: 359px) {
  .mobile-logo-scroll {
    top: 10px !important;
    padding: 8px !important;
  }

  .mobile-logo-scroll img {
    width: 28px !important;
    height: 28px !important;
  }

  .glass-navbar {
    gap: 4px !important; /* Reduced gap to fit all buttons */
    padding: 8px 4px !important; /* Reduced padding */
    padding-bottom: max(8px, calc(8px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove border radius for bottom-stuck appearance */
    bottom: 0 !important; /* Stick to bottom edge */
    left: 0 !important; /* Full width */
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important; /* Distribute buttons evenly */
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important; /* Add top border */
    display: flex !important;
    flex-wrap: nowrap !important;
    overflow-x: hidden !important;
  }

  .glass-navbar .chakra-button {
    min-height: 36px !important; /* Smaller buttons to fit all */
    min-width: 36px !important;
    max-width: 50px !important; /* Smaller max width */
    padding: 6px !important; /* Reduced padding */
    flex: 1 !important; /* Equal width buttons */
    flex-shrink: 0 !important; /* Prevent shrinking */
  }

  .glass-navbar svg {
    width: 18px !important; /* Smaller icons */
    height: 18px !important;
  }
}

/* ===== STANDARD SMALL DEVICES (360px - 374px) ===== */
@media (min-width: 360px) and (max-width: 374px) {
  .mobile-logo-scroll {
    top: 12px !important;
    padding: 9px !important;
  }

  .mobile-logo-scroll img {
    width: 30px !important;
    height: 30px !important;
  }

  .glass-navbar {
    gap: 8px !important;
    padding: 12px 8px !important;
    padding-bottom: max(12px, calc(12px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove all rounded corners */
    bottom: 0 !important; /* Stick to bottom edge */
    left: 0 !important; /* Full width */
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .glass-navbar .chakra-button {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 10px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 55px !important;
  }

  .glass-navbar svg {
    width: 22px !important;
    height: 22px !important;
  }
}

/* ===== STANDARD DEVICES (375px - 389px) ===== */
@media (min-width: 375px) and (max-width: 389px) {
  .mobile-logo-scroll {
    top: 14px !important;
    padding: 10px !important;
  }

  .mobile-logo-scroll img {
    width: 32px !important;
    height: 32px !important;
  }

  .glass-navbar {
    gap: 10px !important;
    padding: 14px 10px !important;
    padding-bottom: max(14px, calc(14px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove all rounded corners */
    bottom: 0 !important; /* Stick to bottom edge */
    left: 0 !important; /* Full width */
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .glass-navbar .chakra-button {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 60px !important;
  }

  .glass-navbar svg {
    width: 24px !important;
    height: 24px !important;
  }
}

/* ===== LARGE DEVICES (390px - 413px) ===== */
@media (min-width: 390px) and (max-width: 413px) {
  .mobile-logo-scroll {
    top: 16px !important;
    padding: 11px !important;
  }

  .mobile-logo-scroll img {
    width: 34px !important;
    height: 34px !important;
  }

  .glass-navbar {
    gap: 12px !important;
    padding: 16px 12px !important;
    padding-bottom: max(16px, calc(16px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove all rounded corners */
    bottom: 0 !important; /* Stick to bottom edge */
    left: 0 !important; /* Full width */
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .glass-navbar .chakra-button {
    min-height: 50px !important;
    min-width: 50px !important;
    padding: 13px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 65px !important;
  }

  .glass-navbar svg {
    width: 26px !important;
    height: 26px !important;
  }
}

/* ===== EXTRA LARGE DEVICES (414px - 427px) ===== */
@media (min-width: 414px) and (max-width: 427px) {
  .mobile-logo-scroll {
    top: 18px !important;
    padding: 12px !important;
  }

  .mobile-logo-scroll img {
    width: 36px !important;
    height: 36px !important;
  }

  .glass-navbar {
    gap: 14px !important;
    padding: 18px 14px !important;
    padding-bottom: max(18px, calc(18px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important; /* Remove all rounded corners */
    bottom: 0 !important; /* Stick to bottom edge */
    left: 0 !important; /* Full width */
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .glass-navbar .chakra-button {
    min-height: 52px !important;
    min-width: 52px !important;
    padding: 14px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 70px !important;
  }

  .glass-navbar svg {
    width: 28px !important;
    height: 28px !important;
  }
}

/* ===== PRO DEVICES (428px - 429px) - iPhone 14 Pro Max (428x926) ===== */
@media (min-width: 428px) and (max-width: 429px) {
  .mobile-logo-scroll {
    top: 18px !important;
    padding: 12px !important;
  }

  .mobile-logo-scroll img {
    width: 36px !important;
    height: 36px !important;
  }

  .glass-navbar {
    gap: 12px !important;
    padding: 14px 12px !important;
    padding-bottom: max(14px, calc(14px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  .glass-navbar .chakra-button {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 8px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 60px !important;
  }

  .glass-navbar svg {
    width: 22px !important;
    height: 22px !important;
  }
}

/* ===== LATEST PRO DEVICES (430px - 479px) - iPhone 14 Pro Max (430x932) ===== */
@media (min-width: 430px) and (max-width: 479px) {
  .mobile-logo-scroll {
    top: 20px !important;
    padding: 14px !important;
  }

  .mobile-logo-scroll img {
    width: 38px !important;
    height: 38px !important;
  }

  .glass-navbar {
    gap: 14px !important;
    padding: 16px 14px !important;
    padding-bottom: max(16px, calc(16px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  .glass-navbar .chakra-button {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 10px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 65px !important;
  }

  .glass-navbar svg {
    width: 24px !important;
    height: 24px !important;
  }
}

/* ===== LARGE MOBILE DEVICES (480px - 539px) ===== */
@media (min-width: 480px) and (max-width: 539px) {
  .mobile-logo-scroll {
    top: 22px !important;
    padding: 16px !important;
  }

  .mobile-logo-scroll img {
    width: 40px !important;
    height: 40px !important;
  }

  .glass-navbar {
    gap: 16px !important;
    padding: 18px 16px !important;
    padding-bottom: max(18px, calc(18px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  .glass-navbar .chakra-button {
    min-height: 50px !important;
    min-width: 50px !important;
    padding: 12px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 70px !important;
  }

  .glass-navbar svg {
    width: 26px !important;
    height: 26px !important;
  }
}

/* ===== ULTRA LARGE MOBILE DEVICES (540px - 599px) ===== */
@media (min-width: 540px) and (max-width: 599px) {
  .mobile-logo-scroll {
    top: 24px !important;
    padding: 18px !important;
  }

  .mobile-logo-scroll img {
    width: 42px !important;
    height: 42px !important;
  }

  .glass-navbar {
    gap: 18px !important;
    padding: 20px 18px !important;
    padding-bottom: max(20px, calc(20px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  .glass-navbar .chakra-button {
    min-height: 52px !important;
    min-width: 52px !important;
    padding: 14px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 75px !important;
  }

  .glass-navbar svg {
    width: 26px !important;
    height: 26px !important;
  }
}

/* ===== MOBILE-TABLET HYBRID DEVICES (600px - 679px) ===== */
@media (min-width: 600px) and (max-width: 679px) {
  .mobile-logo-scroll {
    top: 26px !important;
    padding: 20px !important;
  }

  .mobile-logo-scroll img {
    width: 44px !important;
    height: 44px !important;
  }

  .glass-navbar {
    gap: 20px !important;
    padding: 22px 20px !important;
    padding-bottom: max(22px, calc(22px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  .glass-navbar .chakra-button {
    min-height: 54px !important;
    min-width: 54px !important;
    padding: 16px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 80px !important;
  }

  .glass-navbar svg {
    width: 28px !important;
    height: 28px !important;
  }
}

/* ===== LARGE MOBILE/SMALL TABLET DEVICES (680px - 759px) ===== */
@media (min-width: 680px) and (max-width: 759px) {
  .mobile-logo-scroll {
    top: 28px !important;
    padding: 22px !important;
  }

  .mobile-logo-scroll img {
    width: 46px !important;
    height: 46px !important;
  }

  .glass-navbar {
    gap: 22px !important;
    padding: 24px 22px !important;
    padding-bottom: max(24px, calc(24px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  .glass-navbar .chakra-button {
    min-height: 56px !important;
    min-width: 56px !important;
    padding: 18px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 85px !important;
  }

  .glass-navbar svg {
    width: 28px !important;
    height: 28px !important;
  }
}

/* ===== SPECIFIC SUPPORT FOR 760px WIDTH DEVICES (YOUR CASE) ===== */
@media (min-width: 760px) and (max-width: 767px) {
  .mobile-logo-scroll {
    top: 30px !important;
    padding: 24px !important;
  }

  .mobile-logo-scroll img {
    width: 48px !important;
    height: 48px !important;
  }

  .glass-navbar {
    gap: 24px !important;
    padding: 26px 24px !important;
    padding-bottom: max(26px, calc(26px + env(safe-area-inset-bottom))) !important;
    border-radius: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  .glass-navbar .chakra-button {
    min-height: 58px !important;
    min-width: 58px !important;
    padding: 20px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
    max-width: 90px !important;
  }

  .glass-navbar svg {
    width: 30px !important;
    height: 30px !important;
  }
}

/* ===== LANDSCAPE ORIENTATION ADJUSTMENTS ===== */

/* Ultra-small landscape */
@media (max-width: 279px) and (orientation: landscape) {
  .mobile-logo-scroll {
    top: 4px !important;
    padding: 3px !important;
  }
  
  .glass-navbar {
    bottom: 4px !important;
    padding: 4px 6px !important;
    gap: 3px !important;
  }
  
  .glass-navbar .chakra-button {
    min-height: 28px !important;
    min-width: 28px !important;
  }
}

/* Small landscape */
@media (min-width: 280px) and (max-width: 413px) and (orientation: landscape) {
  .mobile-logo-scroll {
    top: 6px !important;
    padding: 5px !important;
  }
  
  .mobile-logo-scroll img {
    width: calc(100% - 4px) !important;
    height: calc(100% - 4px) !important;
  }
  
  .glass-navbar {
    bottom: 6px !important;
    padding: 6px 12px !important;
    gap: calc(100% - 2px) !important;
  }
  
  .glass-navbar .chakra-button {
    min-height: calc(100% - 4px) !important;
    min-width: calc(100% - 4px) !important;
  }
}

/* Large landscape */
@media (min-width: 414px) and (max-width: 767px) and (orientation: landscape) {
  .mobile-logo-scroll {
    top: 8px !important;
    padding: 6px !important;
  }
  
  .glass-navbar {
    bottom: 8px !important;
    padding: 8px 16px !important;
    gap: 12px !important;
  }
  
  .glass-navbar .chakra-button {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}

/* ===== DEVICE-SPECIFIC OPTIMIZATIONS ===== */

/* iPhone SE (1st gen) - 320x568 - SPECIAL OPTIMIZATION */
@media (width: 320px) and (height: 568px) {
  .mobile-logo-scroll {
    top: 10px !important;
    padding: 6px !important;
  }

  .mobile-logo-scroll img {
    width: 26px !important;
    height: 26px !important;
  }

  .glass-navbar {
    bottom: 0px !important; /* Stick to bottom */
    left: 0px !important;
    right: 0px !important;
    width: 100% !important;
    gap: 2px !important; /* Very tight spacing for 6 buttons */
    padding: 6px 2px !important; /* Minimal padding */
    padding-bottom: 8px !important;
    border-radius: 0 !important;
    justify-content: space-evenly !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    transform: none !important;
  }

  .glass-navbar .chakra-button {
    min-height: 36px !important; /* Compact buttons */
    min-width: 36px !important;
    max-width: 48px !important; /* Limit width to fit 6 buttons */
    padding: 4px !important;
    flex: 1 !important;
    flex-shrink: 0 !important;
  }

  .glass-navbar svg {
    width: 18px !important;
    height: 18px !important;
  }
}

/* iPhone 6/7/8 - 375x667 */
@media (width: 375px) and (height: 667px) {
  .mobile-logo-scroll {
    top: 12px !important;
    padding: 8px !important;
  }

  .mobile-logo-scroll img {
    width: 30px !important;
    height: 30px !important;
  }

  .glass-navbar {
    bottom: 12px !important;
    gap: 12px !important;
    padding: 12px 18px !important;
  }
}

/* iPhone X/XS/11 Pro - 375x812 */
@media (width: 375px) and (height: 812px) {
  .mobile-logo-scroll {
    top: max(16px, env(safe-area-inset-top)) !important;
    padding: 10px !important;
  }

  .mobile-logo-scroll img {
    width: 32px !important;
    height: 32px !important;
  }

  .glass-navbar {
    bottom: max(16px, calc(16px + env(safe-area-inset-bottom))) !important;
    gap: 14px !important;
    padding: 14px 20px !important;
  }

  .glass-navbar .chakra-button {
    min-height: 48px !important;
    min-width: 48px !important;
  }
}

/* iPhone 12 mini - 375x812 */
@media (width: 375px) and (height: 812px) and (-webkit-device-pixel-ratio: 3) {
  .mobile-logo-scroll {
    top: max(18px, env(safe-area-inset-top)) !important;
  }

  .glass-navbar {
    bottom: max(18px, calc(18px + env(safe-area-inset-bottom))) !important;
  }
}

/* iPhone 6/7/8 Plus - 414x736 */
@media (width: 414px) and (height: 736px) {
  .mobile-logo-scroll {
    top: 14px !important;
    padding: 11px !important;
  }

  .mobile-logo-scroll img {
    width: 34px !important;
    height: 34px !important;
  }

  .glass-navbar {
    bottom: 14px !important;
    gap: 16px !important;
    padding: 16px 22px !important;
  }

  .glass-navbar .chakra-button {
    min-height: 50px !important;
    min-width: 50px !important;
  }
}

/* iPhone XR/11 - 414x896 */
@media (width: 414px) and (height: 896px) {
  .mobile-logo-scroll {
    top: max(18px, env(safe-area-inset-top)) !important;
    padding: 12px !important;
  }

  .mobile-logo-scroll img {
    width: 36px !important;
    height: 36px !important;
  }

  .glass-navbar {
    bottom: max(18px, calc(18px + env(safe-area-inset-bottom))) !important;
    gap: 16px !important;
    padding: 18px 24px !important;
  }

  .glass-navbar .chakra-button {
    min-height: 52px !important;
    min-width: 52px !important;
  }
}

/* iPhone 12/13/14 Pro Max - 428x926 */
@media (width: 428px) and (height: 926px) {
  .mobile-logo-scroll {
    top: max(22px, env(safe-area-inset-top)) !important;
    padding: 14px !important;
  }

  .mobile-logo-scroll img {
    width: 38px !important;
    height: 38px !important;
  }

  .glass-navbar {
    bottom: max(12px, calc(12px + env(safe-area-inset-bottom))) !important;
    gap: 12px !important;
    padding: 12px 20px !important;
    /* Ensure horizontal layout for iPhone 14 Pro Max */
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    border-radius: 0 !important;
    min-height: auto !important;
    height: auto !important;
  }

  .glass-navbar .chakra-button {
    min-height: 44px !important;
    min-width: 44px !important;
    flex: 0 0 auto !important;
    margin: 0 !important;
    padding: 8px !important;
  }

  .glass-navbar svg {
    width: 22px !important;
    height: 22px !important;
  }
}

/* iPhone 14 Pro Max - 430x932 - LATEST MODEL */
@media (width: 430px) and (height: 932px) {
  .mobile-logo-scroll {
    top: max(24px, env(safe-area-inset-top)) !important;
    padding: 15px !important;
  }

  .mobile-logo-scroll img {
    width: 40px !important;
    height: 40px !important;
  }

  .glass-navbar {
    bottom: max(12px, calc(12px + env(safe-area-inset-bottom))) !important;
    gap: 14px !important;
    padding: 12px 22px !important;
    /* Ensure horizontal layout for iPhone 14 Pro Max 430x932 */
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    border-radius: 0 !important;
    min-height: auto !important;
    height: auto !important;
    flex-wrap: nowrap !important;
  }

  .glass-navbar .chakra-button {
    min-height: 46px !important;
    min-width: 46px !important;
    flex: 0 0 auto !important;
    margin: 0 !important;
    padding: 9px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .glass-navbar svg {
    width: 24px !important;
    height: 24px !important;
  }
}

/* Samsung Galaxy S8/S9 - 360x740 */
@media (width: 360px) and (height: 740px) {
  .mobile-logo-scroll {
    top: 12px !important;
    padding: 9px !important;
  }

  .mobile-logo-scroll img {
    width: 30px !important;
    height: 30px !important;
  }

  .glass-navbar {
    bottom: 12px !important;
    gap: 10px !important;
    padding: 12px 18px !important;
  }

  .glass-navbar .chakra-button {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}

/* Samsung Galaxy S10/S20 - 360x760 */
@media (width: 360px) and (height: 760px) {
  .mobile-logo-scroll {
    top: 14px !important;
    padding: 10px !important;
  }

  .glass-navbar {
    bottom: 14px !important;
    gap: 12px !important;
  }
}

/* Google Pixel 2/3 - 411x731 */
@media (width: 411px) and (height: 731px) {
  .mobile-logo-scroll {
    top: 16px !important;
    padding: 11px !important;
  }

  .mobile-logo-scroll img {
    width: 34px !important;
    height: 34px !important;
  }

  .glass-navbar {
    bottom: 16px !important;
    gap: 15px !important;
    padding: 17px 23px !important;
  }

  .glass-navbar .chakra-button {
    min-height: 50px !important;
    min-width: 50px !important;
  }
}

/* Google Pixel 4/5 - 393x851 */
@media (width: 393px) and (height: 851px) {
  .mobile-logo-scroll {
    top: 18px !important;
    padding: 12px !important;
  }

  .mobile-logo-scroll img {
    width: 36px !important;
    height: 36px !important;
  }

  .glass-navbar {
    bottom: 18px !important;
    gap: 16px !important;
    padding: 18px 24px !important;
  }

  .glass-navbar .chakra-button {
    min-height: 52px !important;
    min-width: 52px !important;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

@media (max-width: 767px) {
  .mobile-logo-scroll,
  .glass-navbar {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  
  .glass-navbar .chakra-button {
    will-change: transform, background-color;
    contain: layout style paint;
  }
  
  .glass-navbar svg {
    will-change: auto;
    transform: translateZ(0);
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

@media (max-width: 767px) {
  .glass-navbar .chakra-button:focus {
    outline: 2px solid #00CC85;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(0, 204, 133, 0.2);
  }
  
  .glass-navbar .chakra-button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .glass-navbar {
      background: rgba(0, 0, 0, 0.95) !important;
      border: 2px solid rgba(255, 255, 255, 0.8) !important;
    }
    
    .mobile-logo-scroll {
      background: rgba(0, 0, 0, 0.9) !important;
      border: 2px solid rgba(255, 255, 255, 0.8) !important;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .mobile-logo-scroll,
    .glass-navbar,
    .glass-navbar .chakra-button {
      transition: none !important;
      animation: none !important;
    }
  }
}
